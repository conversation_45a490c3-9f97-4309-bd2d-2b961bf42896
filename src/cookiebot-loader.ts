const COOKIEBOT_IDS = {
  EE: '9a2e740d-9929-4e9e-83da-13420793ea4c',
  LV: '72fc07fe-4606-43e2-b090-638c0c480f51',
  LT: '0bb1d40b-c95b-4ca7-b0ec-658d7324ba6d',
} as const;

type Region = keyof typeof COOKIEBOT_IDS;

const getRootDomain = (): string => {
  const hostname = window.location.hostname;
  const parts = hostname.split('.');

  if (parts.length > 2) {
    const rootDomain = parts.slice(-2).join('.');
    return rootDomain;
  }

  return hostname;
};

const setupCookiebotConfig = (): void => {
  const rootDomain = getRootDomain();
  const currentHostname = window.location.hostname;

  (
    window as typeof window & { CookiebotConfig?: { cookieDomain: string } }
  ).CookiebotConfig = {
    cookieDomain: `.${rootDomain}`,
  };

  const setCookie = (
    name: string,
    value: string,
    domain?: string | null,
    path = '/',
    sameSite = 'Lax',
  ): void => {
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);

    const domainPart = domain ? `; domain=${domain}` : '';
    const cookieString = `${name}=${value}${domainPart}; path=${path}; expires=${expiryDate.toUTCString()}; SameSite=${sameSite}`;

    const beforeCookies = document.cookie;
    document.cookie = cookieString;
    const afterCookies = document.cookie;

    console.log('🍪 [COOKIEBOT DEBUG] setCookie result:', {
      success: afterCookies !== beforeCookies,
      beforeLength: beforeCookies.length,
      afterLength: afterCookies.length,
    });
  };

  const getCookieValue = (
    name: string,
    // domainType:
    //  - 'host'  => prefer host-specific cookie (first match) [default, preserves old behavior]
    //  - 'root'  => prefer root-domain cookie (last match)
    //  - 'any'   => same as 'host' (first match)
    domainType: 'host' | 'root' | 'any' = 'host',
    cookieString = document.cookie,
  ): string | null => {
    // Parse cookie string into ordered entries
    const entries = cookieString
      .split(';')
      .map((s) => s.trim())
      .filter(Boolean);

    const matches: string[] = [];
    for (const entry of entries) {
      const [k, ...rest] = entry.split('=');
      if (k === name) {
        matches.push(rest.join('='));
      }
    }

    let result: string | null = null;
    if (matches.length === 0) {
      result = null;
    } else if (domainType === 'root') {
      // prefer last match (root-domain cookie is generally listed later)
      result = matches[matches.length - 1];
    } else {
      // 'host' or 'any' -> prefer first match (host-specific cookie usually listed first)
      result = matches[0];
    }

    console.log('🍪 [COOKIEBOT DEBUG] getCookieValue:', {
      name,
      domainType,
      found: !!result,
      value: result,
      allMatchesCount: matches.length,
      allMatchesPreview: matches.slice(0, 3),
      cookieStringPreview: cookieString.slice(0, 200),
    });

    return result;
  };

  const mirrorCookieBothDomains = (cookieName: string): void => {
    const value = getCookieValue(cookieName);
    if (!value) {
      console.log(
        `🔍 [COOKIEBOT DEBUG] mirrorCookieBothDomains: no value for ${cookieName}, skipping`,
      );
      return;
    }

    const targetRootDomain = `.${rootDomain}`;
    console.log('🔄 [COOKIEBOT DEBUG] mirrorCookieBothDomains:', {
      cookieName,
      valuePreview: value,
      targetRootDomain,
      currentHostname,
    });

    setCookie(cookieName, value, targetRootDomain);

    setCookie(cookieName, value);
  };

  const performMirrors = (): void => {
    mirrorCookieBothDomains('CookieConsent');
  };

  window.addEventListener('CookiebotOnConsentReady', () => {
    performMirrors();
  });

  window.addEventListener('CookiebotOnAccept', () => {
    performMirrors();
  });

  window.addEventListener('CookiebotOnDecline', () => {
    performMirrors();
  });

  window.addEventListener('load', () => {
    setTimeout(() => {
      performMirrors();
    }, 1000);
  });
};

export const loadCookiebot = (region: string): void => {
  const validRegion = (
    COOKIEBOT_IDS[region as Region] ? region : 'EE'
  ) as Region;
  const cookiebotId = COOKIEBOT_IDS[validRegion];

  if (document.getElementById('Cookiebot')) {
    return;
  }

  setupCookiebotConfig();

  const script = document.createElement('script');
  script.id = 'Cookiebot';
  script.src = 'https://consent.cookiebot.com/uc.js';
  script.setAttribute('data-cbid', cookiebotId);
  script.setAttribute('data-blockingmode', 'auto');
  script.type = 'text/javascript';

  document.head.appendChild(script);
};
