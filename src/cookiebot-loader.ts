const COOKIEBOT_IDS = {
  EE: '9a2e740d-9929-4e9e-83da-13420793ea4c',
  LV: '72fc07fe-4606-43e2-b090-638c0c480f51',
  LT: '0bb1d40b-c95b-4ca7-b0ec-658d7324ba6d',
} as const;

type Region = keyof typeof COOKIEBOT_IDS;

const getRootDomain = () => {
  const hostname = window.location.hostname;
  const parts = hostname.split('.');

  if (parts.length > 2) {
    const rootDomain = parts.slice(-2).join('.');
    return rootDomain;
  }

  return hostname;
};

const setupCookiebotConfig = () => {
  const rootDomain = getRootDomain();

  (
    window as typeof window & { CookiebotConfig?: { cookieDomain: string } }
  ).CookiebotConfig = {
    cookieDomain: `.${rootDomain}`,
  };

  const setCookie = (
    name: string,
    value: string,
    domain?: string | null,
    path = '/',
    sameSite = 'Lax',
  ): void => {
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);

    const domainPart = domain ? `; domain=${domain}` : '';
    const cookieString = `${name}=${value}${domainPart}; path=${path}; expires=${expiryDate.toUTCString()}; SameSite=${sameSite}`;

    document.cookie = cookieString;
  };

  const getCookieValue = (name: string): string | null => {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [cookieName, ...cookieValueParts] = cookie.trim().split('=');
      if (cookieName === name) {
        return cookieValueParts.join('=');
      }
    }
    return null;
  };

  const hasHostCookie = (name: string): boolean => {
    return getCookieValue(name) !== null;
  };

  const getRootCookieValue = (name: string): string | null => {
    // Get all cookies and find the one that would be set on root domain
    // This is a simplified approach - in practice, we can't directly read
    // cookies from different domains, but we can check what we set
    return getCookieValue(name);
  };

  // Rule 1: Host-to-Root Sync - when host cookie is updated, sync to root
  const syncHostToRoot = (cookieName: string): void => {
    const hostValue = getCookieValue(cookieName);
    if (hostValue) {
      const targetRootDomain = `.${rootDomain}`;
      setCookie(cookieName, hostValue, targetRootDomain);
      console.log('🔄 [COOKIEBOT] Host-to-Root sync:', {
        cookieName,
        value: hostValue,
        targetDomain: targetRootDomain,
      });
    }
  };

  const syncRootToHostOnLoad = (cookieName: string): void => {
    const hasHost = hasHostCookie(cookieName);
    const rootValue = getRootCookieValue(cookieName);

    if (!hasHost && rootValue) {
      setCookie(cookieName, rootValue);
      console.log('🔄 [COOKIEBOT] Root-to-Host sync on load:', {
        cookieName,
        value: rootValue,
      });
    }
  };

  const performCookieSync = (): void => {
    syncHostToRoot('CookieConsent');
  };

  const performInitialSync = (): void => {
    syncRootToHostOnLoad('CookieConsent');
  };

  window.addEventListener('CookiebotOnConsentReady', () => {
    performCookieSync();
  });

  window.addEventListener('CookiebotOnAccept', () => {
    performCookieSync();
  });

  window.addEventListener('CookiebotOnDecline', () => {
    performCookieSync();
  });

  window.addEventListener('load', () => {
    setTimeout(() => {
      performInitialSync();
    }, 1000);
  });
};

export const loadCookiebot = (region: string): void => {
  const validRegion = (
    COOKIEBOT_IDS[region as Region] ? region : 'EE'
  ) as Region;
  const cookiebotId = COOKIEBOT_IDS[validRegion];

  if (document.getElementById('Cookiebot')) {
    return;
  }

  setupCookiebotConfig();

  const script = document.createElement('script');
  script.id = 'Cookiebot';
  script.src = 'https://consent.cookiebot.com/uc.js';
  script.setAttribute('data-cbid', cookiebotId);
  script.setAttribute('data-blockingmode', 'auto');
  script.type = 'text/javascript';

  document.head.appendChild(script);
};
